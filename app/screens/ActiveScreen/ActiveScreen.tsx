import { observer } from "mobx-react-lite"
import { Text } from "@/components/Text"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { useMemo, useState, useEffect } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { aw } from "@/utils/adaptiveSize"
import DateSelector from "./components/DateSelector"
import { SleepCard } from "./components/SleepCard"
import { mockData } from "@/data/stressData"
import { FitnessCard } from "./components/FitnessCard"
import { HeartRate } from "./components/HeartRate"
import { StepCountCard } from "./components/StepCountCard"
import { BaskCard } from "./components/SunCard"
import { AppStackScreenProps } from "@/navigators/AppNavsigator"
import useHealthData from "@/hooks/useHealthData"
import { useStores } from '@/models/helpers/useStores';


// 定义导航参数类型
type ActiveScreenProps = AppStackScreenProps<"ActiveScreen">

export const ActiveScreen = observer(function ActiveScreen(_props: ActiveScreenProps) {
  // 使用自定义的健康数据 hook
  const {
    initializeHealthKit,
    fetchHeartRateDay,
    fetchHeartRateLast30Days,
  } = useHealthData()
  const { healthStore } = useStores()
  const heartRateDay = healthStore.heartRateDay


  const theme = useAppTheme()
  const [selectedDate, setSelectedDate] = useState(new Date())
  const $styles = useMemo(() => createStyles(theme), [theme])

  const handleDateSelect = (date: Date) => {
    console.log("🚀 ~ handleDateSelect ~ date:", date)
    setSelectedDate(date)
    // 这里可以添加选中日期后的其他逻辑
    console.log("🚀 ~ ActiveScreen ~ heartRateDay:", heartRateDay)
  }

  // 获取图表数据
  const chartData = useMemo(
    () => {
      const data = mockData.stepData.dayStepData
      return data.map((item) => ({
        date: item.time,
        value: item.value,
      }))
    },
    [mockData.stepData.dayStepData],
  )


  // 加载健康数据
  const loadHealthData = async () => {
    try {
      healthStore.setLoading(true)
      healthStore.setError(null)

      // 1. 初始化 HealthKit
      const { success, error } = await initializeHealthKit()

      if (!success) {
        console.warn('HealthKit 初始化失败:', error)
        healthStore.setError('获取健康数据权限失败')
        return
      }

      // 2. 获取所有健康数据（使用 Promise.all 并行获取）
      await Promise.all([
        // fetchHeartRateDay().catch((e: Error) => {
        //   console.warn('获取心率数据失败:', e)
        //   healthStore.setError('获取心率数据失败')
        // }),
        fetchHeartRateLast30Days().catch((e: Error) => {
          console.warn('获取心率数据失败:', e)
          healthStore.setError('获取心率数据失败')
        }),
      ])

    } catch (error) {
      console.error('获取健康数据时出错:', error)
    } finally {
    }
  }

  // 在组件挂载时获取健康数据
  useEffect(() => {
    loadHealthData()

    // 组件卸载时重置状态
    return () => {
    }
  }, [])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={$styles.screenContentContainer}
      safeAreaEdges={["top"]}
    >
      <Text style={$styles.title}>今日活动</Text>
      {/* 日期切换组件 */}
      <DateSelector
        selectedDate={selectedDate}
        onDateSelect={handleDateSelect}
      />
      {/* 睡眠卡片 */}
      <TouchableOpacity activeOpacity={0.8} onPress={() => _props.navigation.navigate('SleepDetailScreen')}>
        <SleepCard sleepStagesData={mockData.sleepData.sleepStagesData} />
      </TouchableOpacity>

      {/* 健身卡片 */}
      <TouchableOpacity activeOpacity={0.8} onPress={() => _props.navigation.navigate('FitnessDetailScreen')}>
        <FitnessCard fitnessData={{
          activityCalories: 270,
          targetActivityCalories: 400,
          exerciseMinutes: 16,
          targetExerciseMinutes: 30,
          standHours: 8,
          targetStandHours: 12,
        }} />
      </TouchableOpacity>

      {/* 心率卡片 */}
      <TouchableOpacity activeOpacity={0.8} onPress={() => _props.navigation.navigate('HeartRateDetailScreen')}>
        <HeartRate heartRateData={heartRateDay} />
      </TouchableOpacity>

      {/* 步数和晒太阳卡片 */}
      <View style={$styles.rowCard}>
        <TouchableOpacity
          style={$styles.stepCardContainer}
          activeOpacity={0.8}
          onPress={() => _props.navigation.navigate('StepCountDetailScreen')}
        >
          <StepCountCard data={chartData} target={mockData.stepData.targetStep} />
        </TouchableOpacity>
        <TouchableOpacity
          style={$styles.basketballCardContainer}
          activeOpacity={0.8}
          onPress={() => {
            // 这里可以添加篮球卡片的跳转逻辑
            console.log('Navigate to Bask Detail')
          }}
        >
          <BaskCard data={[]} target={mockData.stepData.targetStep} />
        </TouchableOpacity>
      </View>
    </Screen>
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,
    title: {
      marginTop: aw(30),
      fontFamily: theme.theme.typography.primary.bold,
      fontSize: aw(22),
      lineHeight: aw(26),
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,
    rowCard: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginTop: aw(16),
    } as ViewStyle,
    stepCardContainer: {
      flex: 1,
      marginRight: aw(16),
    } as ViewStyle,
    basketballCardContainer: {
      flex: 1,
    } as ViewStyle,
  }
}